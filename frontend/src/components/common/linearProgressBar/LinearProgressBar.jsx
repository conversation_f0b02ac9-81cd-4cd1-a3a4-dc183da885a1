import React, { useState, useEffect } from "react";

import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import LinearProgress, {
	linearProgressClasses,
} from "@mui/material/LinearProgress";

export default function LinearProgressBar(props) {
	const [progress, setProgress] = useState(0);
	const [passedTime, changeTimer] = useState(1000);
	const [clockTime, changeClockTime] = useState(0);

	useEffect(() => {
		if (clockTime > 0 && passedTime <= clockTime && progress < 95) {
			let progressPercentage = (passedTime / clockTime) * 100;
			if (progressPercentage > 95) progressPercentage = 95;
			setProgress(progressPercentage);
			const interval = setInterval(() => {
				changeTimer((oldTime) => {
					return oldTime + 1000;
				});
			}, 1000);
			return () => clearInterval(interval);
		}
	}, [passedTime, clockTime]);

	useEffect(() => {
		if (props.showFullBar) setProgress(100);
	}, [props.showFullBar]);

	useEffect(() => {
		if (props.timeInSec && clockTime === 0)
			changeClockTime(props.timeInSec * 1000);
	}, [props.timeInSec]);

	return (
		<Box sx={{ width: "100%" }}>
			<BorderLinearProgress
				variant="determinate"
				value={progress}
				customProgressBarColor={props.barColor}
			/>
		</Box>
	);
}

const BorderLinearProgress = styled(LinearProgress)(
	({ customProgressBarColor }) => ({
		height: 5,
		borderRadius: 5,
		[`&.${linearProgressClasses.colorPrimary}`]: {
			backgroundColor: "#f0f0f0",
		},
		[`& .${linearProgressClasses.bar}`]: {
			borderRadius: 5,
			backgroundColor: customProgressBarColor,
		},
	})
);
