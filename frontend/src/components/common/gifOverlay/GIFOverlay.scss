.gifContainer {
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	position: fixed;
	background: rgba(1, 6, 21, 0.8);
	z-index: 1200;
	display: flex;
	justify-content: center;
	align-items: center;
}

.gifDataContainer {
	border-radius: 12px;
	background: #fff;
	padding: 40px;
	min-width: 800px;
	min-height: 532px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	position: relative;
}

.gifImageContainer {
	height: 200px;
	width: 276px;
}

.gifDataPointsContainer {
	border-radius: 20px;
	border: 1.5px solid #f4f4f4;
	padding: 20px 20px 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24px;
}

.gifDataBox {
	border-radius: 10px;
	padding: 20px;
	width: fit-content;
	display: flex;
	gap: 16px;
	align-items: flex-start;
}

.gifDiscounts {
	@extend .gifDataBox;
	background-color: #f6edfd;
	color: #931ce3;
}

.gifArithmetic {
	color: #c3c8d4;
	font-size: 28px;
	font-weight: 700;
}

.gifProducts {
	@extend .gifDataBox;
	background-color: #efeff9;
	color: #7879cc;
}

.gifStores {
	@extend .gifDataBox;
	background-color: #e9f3f2;
	color: #33b6b1;
}

.gifWeeks {
	@extend .gifDataBox;
	background-color: #f9edf1;
	color: #c87390;
}

.gifDataPoints {
	@extend .gifDataBox;
	background-color: #fef1e1;
	color: #f9aa4b;
	border: 1px solid #f9aa4b;
}

.gifDataValue {
	font-size: 28px;
	font-weight: 700;
	margin-bottom: 5px;
}

.gifTimerContainer {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	margin-top: 24px;
}

.gifFooterContainer {
	display: flex;
	gap: 20px;
	flex-direction: column;
	align-items: center;
}

.gifHorizontalLine {
	height: 1px;
	width: 50%;
	background-color: #b4bac7;
}

.gifAfterProcessContainer {
	display: flex;
	justify-content: center;
	padding: 15px 0;
	width: 45%;
	font-weight: 700;
	font-size: 18px;
	line-height: 115%;
	text-align: center;
	border-radius: 20px;
}

.gifFailedContainer {
	@extend .gifAfterProcessContainer;
	background: #fce9ea;
	color: #da1e28;
}

.gifSuccessContainer {
	@extend .gifAfterProcessContainer;
	background: #e7f5ec;
	color: #1f6d4c;
}

.gifCloseIcon {
	position: absolute;
	top: 40px;
	right: 40px;
	cursor: pointer;
	height: 12px;
}

.failureCross {
	width: 20px;
	height: 20px;
	background: #e25b5b;
	border-radius: 50%;
	color: #ffffff;
	padding: 20px;
	position: absolute;
	left: 120px;
	font-size: 32px;
	font-weight: 600;
	display: flex;
	justify-content: center;
	align-items: center;
	animation: crossVerticalMovement 2s infinite;
}

@keyframes crossVerticalMovement {
	0% {
		top: 10px;
		transform: scale(0);
	}
	10% {
		transform: scale(100%);
	}
	15% {
		transform: scale(110%);
	}
	20% {
		transform: scale(100%);
	}
	25% {
		transform: scale(110%);
	}
	30% {
		transform: scale(100%);
	}
	40% {
		top: 20px;
		transform: scale(110%);
	}
	55% {
		transform: scale(100%);
	}
	70% {
		transform: scale(110%);
	}
	80% {
		top: 0px;
	}
	99% {
		transform: scale(100%);
	}
	100% {
		top: 10px;
		transform: scale(0%);
	}
}

.metric-value {
	font-size: 28px;
	font-weight: 700;
	margin-bottom: 8px;
}

.metric-label {
	font-size: 16px;
	font-weight: 700;
	color: #1F2B4D;
}

.sku-container {
	background-color: #efeff9;
}

.store-container {
	background-color: #e9f3f2;
}