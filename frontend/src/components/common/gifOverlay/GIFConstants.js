import ProductsIcon from "../../../assets/imageAssets/gifProducts.svg?.url";
import StoresIcon from "../../../assets/imageAssets/gifStores.svg?.url";
import WeeksIcon from "../../../assets/imageAssets/gifWeeks.svg?.url";
import DataPointsIcon from "../../../assets/imageAssets/gifDataPoints.svg?.url";
import BubbleIcon from "../../../assets/imageAssets/gifBubble.svg?.url";

export const optimizationGIFConfig = [
    {
        className: "gifProducts",
        isDataBox: true,
        label: "Products",
        key: "product_count",
        icon: <img src={ProductsIcon} alt="ProductsIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifStores",
        isDataBox: true,
        label: "Stores",
        key: "store_count",
        icon: <img src={StoresIcon} alt="StoresIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifWeeks",
        isDataBox: true,
        label: "Days",
        key: "promo_duration",
        icon: <img src={WeeksIcon} alt="WeeksIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifDiscounts",
        isDataBox: true,
        label: "Discount",
        key: "discount_points",
        icon: <img src={BubbleIcon} alt="WeeksIcon" width={30} height={30} />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "=",
    },
    {
        className: "gifDataPoints",
        isDataBox: true,
        label: "Data points",
        key: "data_points",
        icon: <img src={DataPointsIcon} alt="DataPointsIcon" />,
    },
];

export const gifDataPointsConfig = [
    {
        className: "gifProducts",
        isDataBox: true,
        label: "Products",
        key: "product_count",
        icon: <img src={ProductsIcon} alt="ProductsIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifStores",
        isDataBox: true,
        label: "Stores",
        key: "store_count",
        icon: <img src={StoresIcon} alt="StoresIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifWeeks",
        isDataBox: true,
        label: "Days",
        key: "promo_duration",
        icon: <img src={WeeksIcon} alt="WeeksIcon" />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "x",
    },
    {
        className: "gifDiscounts",
        isDataBox: true,
        label: "Scenario",
        key: "scenario_count",
        icon: <img src={BubbleIcon} alt="scenario" width={30} height={30} />,
    },
    {
        isDataBox: false,
        className: "gifArithmetic",
        label: "=",
    },
    {
        className: "gifDataPoints",
        isDataBox: true,
        label: "Data points",
        key: "data_points",
        icon: <img src={DataPointsIcon} alt="DataPointsIcon" />,
    },
];