import React from "react";
import { Breadcrumbs } from "impact-ui";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";

import "./ScreenBreadcrumb.scss";

function ScreenBreadcrumb(props) {
    const navigate = useNavigate();

    const switchToHome = () => {
        window.location.href = "/home";
    };
    const getBreadCrumbList = () => {
        let list = [
            {
                label: "Home",
                onClick: switchToHome,
            },
        ];
        _.forEach(props.breadcrumbList, (item) => {
            list.push({
                label: item.label,
                onClick: () => {
                    navigate(item.path);
                },
            });
        });
        return list;
    };
    return (
        <div className="breadcrumbContainer">
            <Breadcrumbs list={getBreadCrumbList()} />
            {props.children}
        </div>
    );
}

export default ScreenBreadcrumb;
