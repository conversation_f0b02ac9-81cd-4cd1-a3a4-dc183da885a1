import _ from "lodash";
import moment from "moment";

export const checkMandatoryFiltersSelection = (config = [], data = {}) => {
    let flag = true;
    //iterate through all filters in all groups, and check if selected options is present in array.
    _.forEach(config, (config) => {
        _.forEach(config?.groupConfig, (group) => {
            if (group?.filterType === "dropdown" && group?.isMandatory) {
                if (!data[group.filterId]?.selectedOptionsArray?.length) {
                    flag = false;

                }
            }
            if (group?.filterType === "dateRange" && group?.isMandatory) {
                if (!data[group.filterId]?.start_date || !data[group.filterId]?.end_date) {
                    flag = false;
                }
            }
        })
    })
    return flag;
}

export const generatePayloadForModelFilters = (data, allConfig, screen, currentConfig) => {
    let payload = {
        id: currentConfig?.modelId,
        parameters: {
            // screen_name: screen,
        }
    }
    //iterate through all filters in all groups, and set selected filters options into payload's parameter.
    let filtersSection = {}
    _.forEach(allConfig, (config) => {
        _.forEach(config?.groupConfig, (group) => {
            if (group?.filterId === "dateRange") {
                filtersSection.start_date = moment(data.dateRange?.start_date) || null,
                    filtersSection.end_date = moment(data.dateRange?.end_date) || null
            } else {
                filtersSection[group.filterId] = data?.[group.filterId]?.selectedOptionsArray || []
            }
        })
    })
    payload.parameters = {
        ...payload.parameters,
        ...filtersSection
    }
    return payload;
}