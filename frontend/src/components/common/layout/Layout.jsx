import React, { useEffect } from "react";
import uuid from "react-uuid";
import Header from "../header/Header";
import SidebarComponent from "../sidebar/Sidebar";
import LoaderComponent from "../../ui/loaderComponent/LoaderComponent";

function Layout(props) {
	const getGuid = () => {
		const id = uuid(); // generate unique UUID
		sessionStorage.setItem("UNIQ_SSE_KEY", id);
		return id;
	};
	useEffect(() => {
		const uniqueId = getGuid();
		// const token = await getValidToken();
		// const sseListener = new EventSource(
		// 	REACT_APP_API_BASE_URL +
		// 		"sse-output?guid=" +
		// 		uniqueId +
		// 		"&token=" +
		// 		token,
		// 	{
		// 		authorizationHeader: `Bearer ${token}`,
		// 	}
		// );
		// establishSseConnection(sseListener);

		// return () => {
		// 	sseListener.close();
		// 	sessionStorage.removeItem("UNIQ_SSE_KEY");
		// };
	}, []);

	return (
		<div className="display_flex">
			<SidebarComponent />
			<div className="layoutWidth height-full">
				<Header />
				<LoaderComponent>
					<div className="base_color height-full">
						{props.children}
					</div>
				</LoaderComponent>
			</div>
		</div>
	);
}

export default Layout;
